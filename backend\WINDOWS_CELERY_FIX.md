# Windows Celery Worker Fix

## Vấn đề
Lỗi `PermissionError: [WinError 5] Access is denied` khi chạy Celery worker trên Windows là do:

1. **Billiard multiprocessing issues**: Windows có hạn chế với shared memory và semaphores
2. **Pool worker permissions**: Windows không hỗ trợ tốt prefork pool
3. **Process spawning**: Vấn đề với việc tạo child processes

## Giải pháp đã triển khai

### 1. Cấu hình Celery cho Windows (`celery_app.py`)
```python
# Detect platform for Windows-specific configurations
import platform
IS_WINDOWS = platform.system() == "Windows"

# Windows-specific worker settings
worker_pool="solo" if IS_WINDOWS else "prefork",  # Use solo pool on Windows
worker_concurrency=1 if IS_WINDOWS else 4,       # Single process on Windows
```

### 2. Worker startup script tối ưu (`start_worker_windows.py`)
- Sử dụng `--pool=solo` thay vì `prefork`
- Concurrency = 1 (single process)
- Tắt các tính năng không cần thiết: `--without-gossip`, `--without-mingle`, `--without-heartbeat`
- Giới hạn tasks per child: `--max-tasks-per-child=50`

### 3. Script khắc phục sự cố (`troubleshoot_worker.py`)
Kiểm tra và chẩn đoán các vấn đề phổ biến.

## Cách sử dụng

### Phương pháp 1: Sử dụng script tối ưu (Khuyến nghị)
```cmd
cd backend
python start_worker_windows.py
```

### Phương pháp 2: Chạy manual với cấu hình tối ưu
```cmd
cd backend
celery -A celery_app worker --pool=solo --concurrency=1 --loglevel=info --without-gossip --without-mingle --without-heartbeat
```

### Phương pháp 3: Sử dụng threads pool
```cmd
cd backend
celery -A celery_app worker --pool=threads --concurrency=4 --loglevel=info
```

### Phương pháp 4: Sử dụng eventlet (cần cài đặt)
```cmd
pip install eventlet
celery -A celery_app worker --pool=eventlet --concurrency=100 --loglevel=info
```

## Kiểm tra và khắc phục sự cố

### 1. Chạy script chẩn đoán
```cmd
python troubleshoot_worker.py
```

### 2. Kiểm tra Redis
```cmd
redis-cli ping
```

### 3. Kiểm tra Celery connectivity
```cmd
celery -A celery_app inspect ping
```

### 4. Xem worker status
```cmd
celery -A celery_app inspect active
```

## Các lựa chọn Pool cho Windows

### Solo Pool (Khuyến nghị cho Windows)
- **Ưu điểm**: Không có multiprocessing issues, ổn định trên Windows
- **Nhược điểm**: Chỉ 1 process, hiệu suất thấp hơn
- **Sử dụng**: `--pool=solo --concurrency=1`

### Threads Pool
- **Ưu điểm**: Đa luồng, hiệu suất tốt hơn solo
- **Nhược điểm**: GIL limitations, không phù hợp với CPU-intensive tasks
- **Sử dụng**: `--pool=threads --concurrency=4`

### Eventlet Pool
- **Ưu điểm**: Async I/O, hiệu suất cao cho I/O-bound tasks
- **Nhược điểm**: Cần cài đặt thêm, phức tạp hơn
- **Sử dụng**: `pip install eventlet && --pool=eventlet --concurrency=100`

### Gevent Pool
- **Ưu điểm**: Tương tự eventlet, async I/O
- **Nhược điểm**: Cần cài đặt thêm, có thể conflict với một số libraries
- **Sử dụng**: `pip install gevent && --pool=gevent --concurrency=100`

## Environment Variables cho Windows

Thêm vào `.env` hoặc set trong command line:
```
FORKED_BY_MULTIPROCESSING=1
CELERY_OPTIMIZATION=fair
```

## Monitoring

### Flower Dashboard
```cmd
python start_flower.py
```
Truy cập: http://localhost:5555

### Celery Events
```cmd
celery -A celery_app events
```

## Performance Tuning cho Windows

### 1. Giảm overhead
```cmd
--without-gossip --without-mingle --without-heartbeat
```

### 2. Giới hạn memory usage
```cmd
--max-tasks-per-child=50
```

### 3. Tối ưu prefetch
```cmd
--prefetch-multiplier=1
```

## Troubleshooting Common Issues

### Issue 1: "Access is denied"
**Solution**: Sử dụng solo pool thay vì prefork
```cmd
--pool=solo --concurrency=1
```

### Issue 2: Worker không start
**Solution**: 
1. Kiểm tra Redis: `redis-cli ping`
2. Kiểm tra dependencies: `pip install -r requirements.txt`
3. Chạy troubleshoot script: `python troubleshoot_worker.py`

### Issue 3: Tasks không được process
**Solution**:
1. Kiểm tra queue names
2. Kiểm tra worker đang lắng nghe đúng queues
3. Restart worker

### Issue 4: Memory leaks
**Solution**:
1. Giảm `--max-tasks-per-child`
2. Monitor với Flower
3. Restart worker định kỳ

## Production Recommendations

### 1. Sử dụng Docker (Khuyến nghị)
```yaml
# docker-compose.yml
worker:
  build: .
  command: celery -A celery_app worker --pool=solo --concurrency=1
```

### 2. Sử dụng WSL2
Cài đặt Ubuntu trên WSL2 và chạy Celery trong Linux environment.

### 3. Sử dụng Windows Service
Tạo Windows Service để chạy Celery worker như background service.

## Kết luận

Với các thay đổi này, Celery worker sẽ chạy ổn định trên Windows mà không gặp lỗi permission. 
Solo pool tuy có hiệu suất thấp hơn nhưng đảm bảo tính ổn định cao trên Windows.

Để có hiệu suất tốt nhất, khuyến nghị sử dụng Docker hoặc WSL2 để chạy trong Linux environment.
