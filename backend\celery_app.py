import os
from celery import Celery
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Redis configuration
REDIS_HOST = os.getenv("REDIS_HOST", "localhost")
REDIS_PORT = os.getenv("REDIS_PORT", "6379")
REDIS_DB = os.getenv("REDIS_DB", "0")
REDIS_PASSWORD = os.getenv("REDIS_PASSWORD", None)

# Construct Redis URL
if REDIS_PASSWORD:
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
else:
    REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# Create Celery instance
celery_app = Celery(
    "text_classification_worker",
    broker=REDIS_URL,
    backend=REDIS_URL,
    include=[
        "tasks.classification_tasks",
        "tasks.csv_processing_tasks"
    ]
)

# Detect platform for Windows-specific configurations
import platform
IS_WINDOWS = platform.system() == "Windows"

# Celery configuration
celery_app.conf.update(
    # Task serialization
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,

    # Task routing
    task_routes={
        "tasks.classification_tasks.classify_text_task": {"queue": "classification"},
        "tasks.classification_tasks.classify_batch_task": {"queue": "batch_processing"},
        "tasks.csv_processing_tasks.process_csv_task": {"queue": "csv_processing"},
    },

    # Worker configuration - Windows specific
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000 if not IS_WINDOWS else 100,  # Lower on Windows

    # Windows-specific worker settings
    worker_pool="solo" if IS_WINDOWS else "prefork",  # Use solo pool on Windows
    worker_concurrency=1 if IS_WINDOWS else 4,       # Single process on Windows

    # Task time limits
    task_soft_time_limit=300,  # 5 minutes
    task_time_limit=600,       # 10 minutes

    # Result backend settings
    result_expires=3600,       # 1 hour
    result_backend_transport_options={
        "visibility_timeout": 3600,
    },

    # Task retry settings
    task_default_retry_delay=60,
    task_max_retries=3,

    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,

    # Windows-specific broker settings
    broker_connection_retry_on_startup=True,
    broker_connection_retry=True,
    broker_connection_max_retries=10,
)

# Optional: Configure task annotations for specific tasks
celery_app.conf.task_annotations = {
    "tasks.classification_tasks.classify_text_task": {
        "rate_limit": "100/m",  # 100 tasks per minute
    },
    "tasks.csv_processing_tasks.process_csv_task": {
        "rate_limit": "10/m",   # 10 CSV processing tasks per minute
    },
}

if __name__ == "__main__":
    celery_app.start()
