#!/usr/bin/env python3
"""
Celery Worker Startup Script
"""
import os
import sys
import platform
from celery_app import celery_app

if __name__ == "__main__":
    # Set up logging
    import logging
    logging.basicConfig(level=logging.INFO)

    # Detect platform
    IS_WINDOWS = platform.system() == "Windows"

    # Configure worker arguments based on platform
    worker_args = [
        'worker',
        '--loglevel=info',
        '--queues=classification,batch_processing,csv_processing',
        '--hostname=worker@%h'
    ]

    # Add platform-specific arguments
    if IS_WINDOWS:
        # Windows-specific configuration
        worker_args.extend([
            '--pool=solo',           # Use solo pool to avoid multiprocessing issues
            '--concurrency=1',       # Single worker process
            '--without-gossip',      # Disable gossip to reduce overhead
            '--without-mingle',      # Disable mingle to reduce startup time
            '--without-heartbeat',   # Disable heartbeat to reduce overhead
        ])
        print("Starting Celery worker with Windows-optimized settings...")
        print("Pool: solo, Concurrency: 1")
    else:
        # Unix/Linux configuration
        worker_args.extend([
            '--pool=prefork',        # Use prefork pool
            '--concurrency=4',       # Multiple worker processes
        ])
        print("Starting Celery worker with Unix/Linux settings...")
        print("Pool: prefork, Concurrency: 4")

    print(f"Worker arguments: {' '.join(worker_args)}")

    # Start the worker
    try:
        celery_app.worker_main(worker_args)
    except KeyboardInterrupt:
        print("\nWorker stopped by user.")
    except Exception as e:
        print(f"Error starting worker: {e}")
        print("\nTrying alternative startup method...")

        # Alternative startup for Windows
        if IS_WINDOWS:
            import subprocess
            cmd = [
                sys.executable, '-m', 'celery',
                '-A', 'celery_app',
                'worker',
                '--loglevel=info',
                '--pool=solo',
                '--concurrency=1',
                '--queues=classification,batch_processing,csv_processing'
            ]
            print(f"Running: {' '.join(cmd)}")
            subprocess.run(cmd)
