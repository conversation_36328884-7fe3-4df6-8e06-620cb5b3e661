#!/usr/bin/env python3
"""
Windows-Optimized Celery Worker Startup Script
This script addresses Windows-specific issues with Celery multiprocessing
"""
import os
import sys
import platform
import subprocess
import time
import signal
from pathlib import Path

def check_redis_connection():
    """Check if Redis is running and accessible"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=5)
        r.ping()
        print("✓ Redis connection successful")
        return True
    except Exception as e:
        print(f"✗ Redis connection failed: {e}")
        print("Please make sure Redis is running:")
        print("  - Windows: Download from https://github.com/microsoftarchive/redis/releases")
        print("  - Or use Docker: docker run -d -p 6379:6379 redis:alpine")
        return False

def setup_environment():
    """Setup environment variables and paths"""
    # Add current directory to Python path
    current_dir = Path(__file__).parent.absolute()
    if str(current_dir) not in sys.path:
        sys.path.insert(0, str(current_dir))
    
    # Set environment variables for Windows
    os.environ.setdefault('FORKED_BY_MULTIPROCESSING', '1')
    os.environ.setdefault('CELERY_OPTIMIZATION', 'fair')
    
    print(f"Working directory: {current_dir}")

def start_worker_direct():
    """Start worker using direct Celery app method"""
    try:
        from celery_app import celery_app
        
        print("Starting Celery worker (direct method)...")
        
        # Configure for Windows
        worker_args = [
            'worker',
            '--loglevel=info',
            '--pool=solo',                    # Solo pool for Windows
            '--concurrency=1',                # Single process
            '--queues=classification,batch_processing,csv_processing',
            '--hostname=worker@windows',
            '--without-gossip',               # Reduce overhead
            '--without-mingle',               # Reduce startup time
            '--without-heartbeat',            # Reduce overhead
            '--max-tasks-per-child=50',       # Restart worker after 50 tasks
        ]
        
        print(f"Worker arguments: {' '.join(worker_args)}")
        celery_app.worker_main(worker_args)
        
    except Exception as e:
        print(f"Direct method failed: {e}")
        return False
    return True

def start_worker_subprocess():
    """Start worker using subprocess method"""
    try:
        print("Starting Celery worker (subprocess method)...")
        
        cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'celery_app',
            'worker',
            '--loglevel=info',
            '--pool=solo',
            '--concurrency=1',
            '--queues=classification,batch_processing,csv_processing',
            '--hostname=worker@windows',
            '--without-gossip',
            '--without-mingle',
            '--without-heartbeat',
            '--max-tasks-per-child=50',
        ]
        
        print(f"Running command: {' '.join(cmd)}")
        
        # Start the process
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Handle Ctrl+C gracefully
        def signal_handler(signum, frame):
            print("\nShutting down worker...")
            process.terminate()
            process.wait()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        # Stream output
        for line in process.stdout:
            print(line.strip())
            
        return process.returncode == 0
        
    except Exception as e:
        print(f"Subprocess method failed: {e}")
        return False

def main():
    """Main function to start the worker"""
    print("=" * 60)
    print("Windows-Optimized Celery Worker Startup")
    print("=" * 60)
    
    # Check if we're on Windows
    if platform.system() != "Windows":
        print("This script is optimized for Windows. Use start_worker.py instead.")
        sys.exit(1)
    
    # Setup environment
    setup_environment()
    
    # Check Redis connection
    if not check_redis_connection():
        print("\nPlease start Redis first, then run this script again.")
        sys.exit(1)
    
    print("\nStarting Celery worker with Windows optimizations...")
    print("Configuration:")
    print("  - Pool: solo (single process)")
    print("  - Concurrency: 1")
    print("  - Queues: classification, batch_processing, csv_processing")
    print("  - Max tasks per child: 50")
    print("  - Optimizations: no gossip, no mingle, no heartbeat")
    print()
    
    # Try direct method first
    print("Attempting direct startup method...")
    if start_worker_direct():
        return
    
    # Fallback to subprocess method
    print("\nFalling back to subprocess method...")
    if start_worker_subprocess():
        return
    
    # If both methods fail
    print("\n" + "=" * 60)
    print("WORKER STARTUP FAILED")
    print("=" * 60)
    print("Both startup methods failed. Please try:")
    print("1. Check if all dependencies are installed:")
    print("   pip install -r requirements.txt")
    print("2. Make sure Redis is running")
    print("3. Try running manually:")
    print("   celery -A celery_app worker --pool=solo --concurrency=1 --loglevel=info")
    print("4. Check the logs above for specific error messages")

if __name__ == "__main__":
    main()
