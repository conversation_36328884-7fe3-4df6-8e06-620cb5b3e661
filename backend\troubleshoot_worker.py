#!/usr/bin/env python3
"""
Celery Worker Troubleshooting Script for Windows
This script helps diagnose and fix common Celery worker issues on Windows
"""
import os
import sys
import platform
import subprocess
import importlib.util
from pathlib import Path

def check_python_version():
    """Check Python version compatibility"""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("⚠️  Warning: Python 3.8+ is recommended for Celery")
        return False
    else:
        print("✓ Python version is compatible")
        return True

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'celery',
        'redis',
        'billiard',
        'kombu',
        'vine'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            spec = importlib.util.find_spec(package)
            if spec is None:
                missing_packages.append(package)
            else:
                print(f"✓ {package} is installed")
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"✗ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True

def check_redis_connection():
    """Check Redis connection"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=5)
        r.ping()
        print("✓ Redis is running and accessible")
        
        # Check Redis info
        info = r.info()
        print(f"  Redis version: {info.get('redis_version', 'unknown')}")
        print(f"  Used memory: {info.get('used_memory_human', 'unknown')}")
        return True
        
    except Exception as e:
        print(f"✗ Redis connection failed: {e}")
        print("Solutions:")
        print("  1. Install Redis for Windows:")
        print("     https://github.com/microsoftarchive/redis/releases")
        print("  2. Or use Docker: docker run -d -p 6379:6379 redis:alpine")
        print("  3. Or use WSL: sudo apt install redis-server && redis-server")
        return False

def check_celery_config():
    """Check Celery configuration"""
    try:
        from celery_app import celery_app
        print("✓ Celery app imported successfully")
        
        # Check broker URL
        broker_url = celery_app.conf.broker_url
        print(f"  Broker URL: {broker_url}")
        
        # Check backend URL
        backend_url = celery_app.conf.result_backend
        print(f"  Result backend: {backend_url}")
        
        # Check task routes
        routes = celery_app.conf.task_routes
        print(f"  Task routes configured: {len(routes) if routes else 0}")
        
        return True
        
    except Exception as e:
        print(f"✗ Celery configuration error: {e}")
        return False

def check_worker_permissions():
    """Check Windows permissions and suggest fixes"""
    print("Checking Windows-specific issues...")
    
    # Check if running as administrator
    try:
        import ctypes
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        if is_admin:
            print("✓ Running with administrator privileges")
        else:
            print("⚠️  Not running as administrator")
            print("  Consider running as administrator if issues persist")
    except:
        print("? Could not check administrator status")
    
    # Check for antivirus interference
    print("⚠️  If you have antivirus software, it might interfere with Celery")
    print("  Consider adding Python and your project folder to antivirus exclusions")
    
    return True

def suggest_fixes():
    """Suggest common fixes for Windows Celery issues"""
    print("\n" + "=" * 60)
    print("COMMON FIXES FOR WINDOWS CELERY ISSUES")
    print("=" * 60)
    
    print("\n1. Use solo pool instead of prefork:")
    print("   celery -A celery_app worker --pool=solo --concurrency=1")
    
    print("\n2. Disable problematic features:")
    print("   --without-gossip --without-mingle --without-heartbeat")
    
    print("\n3. Set environment variables:")
    print("   set FORKED_BY_MULTIPROCESSING=1")
    print("   set CELERY_OPTIMIZATION=fair")
    
    print("\n4. Use threads instead of processes:")
    print("   celery -A celery_app worker --pool=threads --concurrency=4")
    
    print("\n5. Install Windows-specific packages:")
    print("   pip install eventlet")
    print("   celery -A celery_app worker --pool=eventlet --concurrency=100")
    
    print("\n6. Use gevent (alternative):")
    print("   pip install gevent")
    print("   celery -A celery_app worker --pool=gevent --concurrency=100")

def run_test_worker():
    """Try to run a test worker"""
    print("\n" + "=" * 60)
    print("TESTING WORKER STARTUP")
    print("=" * 60)
    
    try:
        cmd = [
            sys.executable, '-m', 'celery',
            '-A', 'celery_app',
            'inspect', 'ping'
        ]
        
        print("Testing Celery connectivity...")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✓ Celery is working correctly")
            return True
        else:
            print(f"✗ Celery test failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ Celery test timed out")
        return False
    except Exception as e:
        print(f"✗ Celery test error: {e}")
        return False

def main():
    """Main troubleshooting function"""
    print("=" * 60)
    print("CELERY WORKER TROUBLESHOOTING FOR WINDOWS")
    print("=" * 60)
    
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Architecture: {platform.architecture()[0]}")
    print()
    
    # Run all checks
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Redis Connection", check_redis_connection),
        ("Celery Configuration", check_celery_config),
        ("Windows Permissions", check_worker_permissions),
    ]
    
    all_passed = True
    for name, check_func in checks:
        print(f"\n--- {name} ---")
        if not check_func():
            all_passed = False
    
    # Test worker if all checks passed
    if all_passed:
        run_test_worker()
    
    # Always show suggestions
    suggest_fixes()
    
    print("\n" + "=" * 60)
    if all_passed:
        print("✓ All checks passed! Try running the worker now.")
    else:
        print("✗ Some issues found. Please fix them and try again.")
    print("=" * 60)

if __name__ == "__main__":
    main()
